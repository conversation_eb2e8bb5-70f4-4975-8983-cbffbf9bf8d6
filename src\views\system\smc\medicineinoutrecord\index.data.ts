import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
import { MedicineApi } from "@/api/system/smc/medicine";
import { DeptApi } from "@/api/system/dept/deptOption";
import { CabinetApi } from "@/api/system/smc/cabinet";
import { CabinetDrawerApi } from "@/api/system/smc/cabinetdrawer";
import { CabinetSlotApi } from "@/api/system/smc/cabinetslot";
import { UsersApi } from "@/api/system/user/userOption";

export const formSearchConfig = {
  itemList: [
    {
      component: 'select',
      label: '药柜',
      prop: 'cabinetId',
      placeholder: '请选择药柜',
      params: {},
      filterable: true,
      fieldNames: { label: 'cabinetIdName', value: 'id', id: 'id', parentId: 'parentId' },
      api: CabinetApi.getCabinetList,
    },
    {
      component: 'select',
      label: '所属抽屉',
      prop: 'drawerId',
      placeholder: '请选择所属抽屉',
      params: {},
      filterable: true,
      fieldNames: { label: 'drawerIdName', value: 'id', id: 'id', parentId: 'parentId' },
      api: CabinetDrawerApi.getCabinetDrawerList,
    },
    {
      component: 'input',
      label: '关联单据编号',
      prop: 'documentNo',
      placeholder: '请输入关联单据编号'
    },
    {
      component: 'select',
      label: '操作人员ID',
      prop: 'operatorId',
      placeholder: '请选择操作人员ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'nickname', value: 'id', id: 'id', parentId: 'parentId' },
      api: UsersApi.getUsersList,
    },
    {
      component: 'datePickerRange',
      label: '操作时间',
      prop: 'operationTime',
      startPlaceholder: '操作时间开始日期',
      endPlaceholder: '操作时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'select',
      label: '操作类型',
      prop: 'operationType',
      placeholder: '请选择操作类型',
      params: {},
      filterable: true,
      // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_OPERATION_TYPE)
    },
    {
      component: 'select',
      label: '业务类型',
      prop: 'businessType',
      placeholder: '请选择业务类型',
      params: {},
      filterable: true,
      // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_BUSINESS_TYPE)
    },
    {
      component: 'select',
      label: '药品',
      prop: 'medicineId',
      placeholder: '请选择药品',
      params: {},
      filterable: true,
      fieldNames: { label: 'medicineIdName', value: 'id', id: 'id', parentId: 'parentId' },
      api: MedicineApi.getMedicineList,
    },
    {
      component: 'input',
      label: '单据药品批次号',
      prop: 'batchNumber',
      placeholder: '请输入单据药品批次号'
    },
    // {
    //   component: 'input',
    //   label: '操作记录单号',
    //   prop: 'recordNumber',
    //   placeholder: '请输入操作记录单号'
    // },
    // {
    //   component: 'input',
    //   label: '单据药品批次关联ID',
    //   prop: 'documentMedicineId',
    //   placeholder: '请输入单据药品批次关联ID'
    // },


    // {
    //   component: 'input',
    //   label: '药品追溯码',
    //   prop: 'traceCode',
    //   placeholder: '请输入药品追溯码'
    // },
    // {
    //   component: 'datePickerRange',
    //   label: '生产日期',
    //   prop: 'productionDate',
    //   startPlaceholder: '生产日期开始日期',
    //   endPlaceholder: '生产日期结束日期',
    //   dateFormate: 'YYYY-MM-DD HH:mm:ss'
    // },
    // {
    //   component: 'datePickerRange',
    //   label: '有效期截止日期',
    //   prop: 'expiryDate',
    //   startPlaceholder: '有效期截止日期开始日期',
    //   endPlaceholder: '有效期截止日期结束日期',
    //   dateFormate: 'YYYY-MM-DD HH:mm:ss'
    // },


    // {
    //   component: 'input',
    //   label: '单据ID',
    //   prop: 'documentId',
    //   placeholder: '请输入单据ID'
    // },
    // {
    //   component: 'treeSelect',
    //   label: '部门ID',
    //   prop: 'deptId',
    //   placeholder: '请选择部门ID',
    //   params: {},
    //   filterable: true,
    //   fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
    //   api: DeptApi.getDeptList,
    // },

    // {
    //   component: 'select',
    //   label: '格口ID（取出或放入格口）',
    //   prop: 'slotId',
    //   placeholder: '请选择格口ID（取出或放入格口）',
    //   params: {},
    //   filterable: true,
    //   fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
    //   api: CabinetSlotApi.getCabinetSlotList,
    // },
    // {
    //   component: 'input',
    //   label: '格口位置描述',
    //   prop: 'slotPosition',
    //   placeholder: '请输入格口位置描述'
    // },
    // {
    //   component: 'number',
    //   label: '计划数量（入柜：计划入柜数量，出柜：应取数量）',
    //   prop: 'plannedQuantity',
    //   placeholder: '请输入计划数量（入柜：计划入柜数量，出柜：应取数量）'
    // },
    // {
    //   component: 'number',
    //   label: '识别数量（入柜专用）',
    //   prop: 'recognizedQuantity',
    //   placeholder: '请输入识别数量（入柜专用）'
    // },
    // {
    //   component: 'number',
    //   label: '实际数量（入柜：实际入柜数量，出柜：实取数量）',
    //   prop: 'actualQuantity',
    //   placeholder: '请输入实际数量（入柜：实际入柜数量，出柜：实取数量）'
    // },
    // {
    //   component: 'number',
    //   label: '放回数量（出柜专用）',
    //   prop: 'returnedQuantity',
    //   placeholder: '请输入放回数量（出柜专用）'
    // },
    // {
    //   component: 'select',
    //   label: '放回抽屉ID（出柜放回格口的抽屉）',
    //   prop: 'returnedDrawerId',
    //   placeholder: '请选择放回抽屉ID（出柜放回格口的抽屉）',
    //   params: {},
    //   filterable: true,
    //   fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
    //   api: CabinetDrawerApi.getCabinetDrawerList,
    // },
    // {
    //   component: 'select',
    //   label: '放回格口ID（出柜放回格口ID）',
    //   prop: 'returnedSlotId',
    //   placeholder: '请选择放回格口ID（出柜放回格口ID）',
    //   params: {},
    //   filterable: true,
    //   fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
    //   api: CabinetSlotApi.getCabinetSlotList,
    // },
    // {
    //   component: 'input',
    //   label: '格口位置描述',
    //   prop: 'returnedSlotPosition',
    //   placeholder: '请输入格口位置描述'
    // },
    // {
    //   component: 'select',
    //   label: '操作状态:pending/in_progress/completed/failed',
    //   prop: 'operationStatus',
    //   placeholder: '请选择操作状态:pending/in_progress/completed/failed',
    //   params: {},
    //   filterable: true,
    //           // todo 请完善字典类型 到 DICT_TYPE中
    //   options: getStrDictOptions(DICT_TYPE.SMC_OPERATION_STATUS)
    // },


    // {
    //   component: 'input',
    //   label: '药品追溯码列表(JSON数组)',
    //   prop: 'traceCodes',
    //   placeholder: '请输入药品追溯码列表(JSON数组)'
    // },
    // {
    //   component: 'input',
    //   label: '操作视频链接',
    //   prop: 'videoUrl',
    //   placeholder: '请输入操作视频链接'
    // },
    // {
    //   component: 'number',
    //   label: '是否已禁用 0=否（正常）,1=是（停用）',
    //   prop: 'status',
    //   placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    // },
    // {
    //   component: 'input',
    //   label: '备注',
    //   prop: 'remark',
    //   placeholder: '请输入备注'
    // },
    // {
    //   component: 'datePickerRange',
    //   label: '创建时间',
    //   prop: 'createTime',
    //   startPlaceholder: '创建时间开始日期',
    //   endPlaceholder: '创建时间结束日期',
    //   dateFormate: 'YYYY-MM-DD HH:mm:ss'
    // },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '药柜',
      prop: 'cabinetIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '所属抽屉',
      prop: 'drawerIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '格口',
      prop: 'slotIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品',
      prop: 'medicineIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '单据药品批次号',
      prop: 'batchNumber',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '制剂规格',
      prop: 'specification',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '关联单据编号',
      prop: 'documentNo',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '操作类型',
      prop: 'operationType',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_OPERATION_TYPE, row.operationType)
    },
    {
      label: '业务类型',
      prop: 'businessType',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_BUSINESS_TYPE, row.businessType)
    },
    {
      label: '识别数量',
      prop: 'recognizedQuantity',
      tooltip: true,
      minWidth: 100,
      slot: 'recognizedQuantity'
    },
    {
      label: '实际数量',
      prop: 'actualQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '操作人员',
      prop: 'operatorIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '操作视频链接',
      prop: 'videoUrl',
      tooltip: true,
      minWidth: 120,
      slot: 'videoUrl'
    },
    {
      label: '操作时间',
      prop: 'operationTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    // {
    //   label: '操作记录单号',
    //   prop: 'recordNumber',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '单据药品批次关联ID',
    //   prop: 'documentMedicineIdName',
    //   tooltip: true,
    //   minWidth: 100
    // },


    // {
    //   label: '药品追溯码',
    //   prop: 'traceCode',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '生产日期',
    //   prop: 'productionDate',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '药品有效期(月)',
    //   prop: 'shelfLife',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '有效期截止日期',
    //   prop: 'expiryDate',
    //   tooltip: true,
    //   minWidth: 100
    // },


    // {
    //   label: '单据ID',
    //   prop: 'documentIdName',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '部门ID',
    //   prop: 'deptIdName',
    //   tooltip: true,
    //   minWidth: 100
    // },

    // {
    //   label: '格口位置描述',
    //   prop: 'slotPosition',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '计划数量（入柜：计划入柜数量，出柜：应取数量）',
    //   prop: 'plannedQuantity',
    //   tooltip: true,
    //   minWidth: 100
    // },

    // {
    //   label: '放回数量（出柜专用）',
    //   prop: 'returnedQuantity',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '放回抽屉ID（出柜放回格口的抽屉）',
    //   prop: 'returnedDrawerIdName',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '放回格口ID（出柜放回格口ID）',
    //   prop: 'returnedSlotIdName',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '格口位置描述',
    //   prop: 'returnedSlotPosition',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '操作状态:pending/in_progress/completed/failed',
    //   prop: 'operationStatus',
    //   minWidth: 100,
    //   filter: (row: any) => getDictLabel(DICT_TYPE.SMC_OPERATION_STATUS, row.operationStatus)
    // },


    // {
    //   label: '药品追溯码列表(JSON数组)',
    //   prop: 'traceCodes',
    //   tooltip: true,
    //   minWidth: 100
    // },

    // {
    //   label: '是否已禁用 0=否（正常）,1=是（停用）',
    //   prop: 'status',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '备注',
    //   prop: 'remark',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '创建时间',
    //   prop: 'createTime',
    //   dateFormate: 'YYYY-MM-DD HH:mm:ss',
    //   minWidth: 180
    // },
  ]
})

export const formConfig = ref([
  {
    title: '药品出入柜操作记录',
    items: [
    ]
  }
])

// 识别记录弹窗搜索条件配置
export const recognitionSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '追溯编码',
      prop: 'searchKeyword',
      placeholder: '请输入追溯编码',
      span: 6
    },
    {
      component: 'input',
      label: '药品批次',
      prop: 'medicineCode',
      placeholder: '药品批次',
      span: 6
    },
    {
      component: 'datePickerRange',
      label: '药品生产日期范围',
      prop: 'productionDateRange',
      startPlaceholder: '药品生产日期范围',
      endPlaceholder: '药品有效期范围',
      dateFormate: 'YYYY-MM-DD',
      span: 6
    },
    {
      component: 'datePickerRange',
      label: '药品有效期范围',
      prop: 'expiryDateRange',
      startPlaceholder: '药品有效期范围',
      endPlaceholder: '药品有效期范围',
      dateFormate: 'YYYY-MM-DD',
      span: 6
    }
  ]
}

// 识别记录弹窗表格配置
export const recognitionTableConfig = {
  columns: [
    {
      label: '批号',
      prop: 'batchNumber',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品追溯码',
      prop: 'traceCode',
      tooltip: true,
      minWidth: 120
    },
    {
      label: '生产批号',
      prop: 'batchNumber',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '生产日期',
      prop: 'productionDate',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '剂型',
      prop: 'dosageForm',
      tooltip: true,
      minWidth: 80
    },
    {
      label: '包装规格',
      prop: 'specification',
      tooltip: true,
      minWidth: 80
    },
    {
      label: '包装规格比',
      prop: 'packageCount',
      tooltip: true,
      minWidth: 80
    },
    {
      label: '药品有效期',
      prop: 'shelfLife',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品有效期明细日期',
      prop: 'expiryDate',
      tooltip: true,
      minWidth: 120
    }
  ]
}


