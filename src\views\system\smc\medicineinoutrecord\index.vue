<template>
  <BasicPage :tabs="['药品出入柜操作记录管理']">
    <template #action>
      <BasicButtonImport perm-code="system:medicine-in-out-record:import" file-name="药品出入柜操作记录管理"
        :template-api="MedicineInOutRecordApi.importTemplate"
        :import-api="MedicineInOutRecordApi.importMedicineInOutRecord"
        :exportError-file-api="MedicineInOutRecordApi.exportErrorFile" @success="handlerImportSuccess" />
      <BasicButtonExport perm-code="system:medicine-in-out-record:export" file-name="药品出入柜操作记录管理"
        :params="{ ...searchForm }" :export-api="MedicineInOutRecordApi.exportMedicineInOutRecord" />
      <el-button v-hasPermi="['system:medicine-in-out-record:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register">
      <template #recognizedQuantity="{ row }">
        <span class="text-blue-600 cursor-pointer hover:underline" @click="handleViewRecognitionRecord(row)">
          {{ row.recognizedQuantity || '-' }}
        </span>
      </template>
      <template #videoUrl="{ row }">
        <el-button type="primary" size="small" @click="handleViewVideo(row.videoUrl)">
          查看视频
        </el-button>
      </template>
    </BasicTable>

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->

    <!-- 识别记录弹窗 -->
    <el-dialog v-model="recognitionDialogVisible" title="注射用瑞芬太尼 识别记录" width="80%"
      :before-close="handleCloseRecognitionDialog">
      <div class="recognition-record-content">
        <div class="mb-4">
          <BasicFormSearch :config="recognitionSearchConfig" v-model:data="recognitionSearchForm"
            @search="searchRecognitionData" @reset="resetRecognitionSearch" />
        </div>

        <el-table :data="recognitionTableData" border style="width: 100%">
          <el-table-column type="index" label="批号" width="80" />
          <el-table-column prop="traceCode" label="药品追溯码" min-width="120">
            <template #default="{ row }">
              <span class="text-blue-600">{{ row.traceCode }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="batchNumber" label="生产批号" min-width="100" />
          <el-table-column prop="productionDate" label="生产日期" min-width="100" />
          <el-table-column prop="dosageForm" label="剂型" min-width="80" />
          <el-table-column prop="specification" label="包装规格" min-width="80" />
          <el-table-column prop="packageCount" label="包装规格比" min-width="80" />
          <el-table-column prop="shelfLife" label="药品有效期" min-width="100" />
          <el-table-column prop="expiryDate" label="药品有效期明细日期" min-width="120" />
        </el-table>
      </div>

      <template #footer>
        <el-button @click="recognitionDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig, recognitionSearchConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { MedicineInOutRecordApi } from '@/api/system/smc/medicineinoutrecord'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const totalColumns = [...tableData.value.columns]

const [register, { reload }] = useTable({

  api: MedicineInOutRecordApi.getMedicineInOutRecordPage,
  columns: totalColumns,

  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: MedicineInOutRecordApi.getMedicineInOutRecord,
    submitApi: id ? MedicineInOutRecordApi.updateMedicineInOutRecord : MedicineInOutRecordApi.createMedicineInOutRecord
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

// 查看视频
function handleViewVideo(videoUrl: string) {
  if (videoUrl) {
    window.open(videoUrl, '_blank')
  } else {
    message.warning('视频链接为空')
  }
}

// 查看识别记录
const recognitionDialogVisible = ref(false)
const recognitionRecordData = ref<any>({})
const recognitionSearchForm = ref<{ [key: string]: any }>({})
const recognitionTableData = ref([])

function handleViewRecognitionRecord(row: any) {
  recognitionRecordData.value = row
  recognitionDialogVisible.value = true
}

function handleCloseRecognitionDialog() {
  recognitionDialogVisible.value = false
  resetRecognitionSearch()
}

function searchRecognitionData() {
  // 这里可以添加实际的搜索逻辑
  console.log('搜索条件:', recognitionSearchForm.value)
}

function resetRecognitionSearch() {
  recognitionSearchForm.value = {}
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await MedicineInOutRecordApi.deleteMedicineInOutRecord(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>